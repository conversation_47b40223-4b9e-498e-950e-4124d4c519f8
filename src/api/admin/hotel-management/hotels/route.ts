import {
  AuthenticatedMedusaRequest,
  MedusaResponse,
} from "@camped-ai/framework/http";
import { z } from "zod";
import {
  PostAdminCreateHotel,
  PostAdminDeleteHotel,
  PostAdminUpdateHotel,
} from "./validators";
import { CreateHotelWorkflow } from "src/workflows/hotel-management/hotel/create-hotel";
import { UpdateHotelWorkflow } from "src/workflows/hotel-management/hotel/update-hotel";
import { DeleteHotelWorkflow } from "src/workflows/hotel-management/hotel/delete-hotel";
import { ContainerRegistrationKeys, Modules } from "@camped-ai/framework/utils";
import { RBAC_MODULE } from "../../../../modules/rbac";
import RbacModuleService from "../../../../modules/rbac/service";
import { UserRole } from "../../../../modules/rbac/types";

type PostAdminCreateHotelType = z.infer<typeof PostAdminCreateHotel>;
type PostAdminDeleteHotelType = z.infer<typeof PostAdminDeleteHotel>;
type PostAdminUpdateHotelType = z.infer<typeof PostAdminUpdateHotel>;

export const POST = async (
  req: AuthenticatedMedusaRequest<PostAdminCreateHotelType>,
  res: MedusaResponse
) => {
  try {
    // Check permission to create hotels
    const rbacService: RbacModuleService = req.scope.resolve(RBAC_MODULE);
    const userService = req.scope.resolve(Modules.USER);

    if (req.auth_context?.actor_id) {
      const user = await userService.retrieveUser(req.auth_context.actor_id);
      const userWithRole = {
        ...user,
        created_at: user.created_at.toISOString(),
        updated_at: user.updated_at.toISOString(),
      };
      const hasPermission = await rbacService.hasPermission(
        userWithRole,
        "hotel_management:create" as any
      );

      if (!hasPermission) {
        return res.status(403).json({
          error: "Insufficient permissions",
          required_permission: "hotel_management:create",
        });
      }
    } else {
      return res.status(401).json({ error: "Authentication required" });
    }

    const { result } = await CreateHotelWorkflow(req.scope).run({
      //@ts-ignore
      input: req.body,
    });
    res.json({ hotel: result });
  } catch (error) {
    console.error("Error creating hotel:", error);
    res.status(500).json({ error: "Failed to create hotel" });
  }
};

export const PUT = async (
  req: AuthenticatedMedusaRequest<PostAdminUpdateHotelType>,
  res: MedusaResponse
) => {
  try {
    // Check permission to edit hotels
    const rbacService: RbacModuleService = req.scope.resolve(RBAC_MODULE);
    const userService = req.scope.resolve(Modules.USER);

    if (req.auth_context?.actor_id) {
      const user = await userService.retrieveUser(req.auth_context.actor_id);
      const userWithRole = {
        ...user,
        created_at: user.created_at.toISOString(),
        updated_at: user.updated_at.toISOString(),
      };
      const hasPermission = await rbacService.hasPermission(
        userWithRole,
        "hotel_management:edit" as any
      );

      if (!hasPermission) {
        return res.status(403).json({
          error: "Insufficient permissions",
          required_permission: "hotel_management:edit",
        });
      }
    } else {
      return res.status(401).json({ error: "Authentication required" });
    }

    const { result } = await UpdateHotelWorkflow(req.scope).run({
      //@ts-ignore
      input: req.body,
    });
    res.json({ hotel: result });
  } catch (error) {
    console.error("Error updating hotel:", error);
    res.status(500).json({ error: "Failed to update hotel" });
  }
};
export const DELETE = async (
  req: AuthenticatedMedusaRequest<PostAdminDeleteHotelType>,
  res: MedusaResponse
) => {
  try {
    // Check permission to delete hotels
    const rbacService: RbacModuleService = req.scope.resolve(RBAC_MODULE);
    const userService = req.scope.resolve(Modules.USER);

    if (req.auth_context?.actor_id) {
      const user = await userService.retrieveUser(req.auth_context.actor_id);
      const userWithRole = {
        ...user,
        created_at: user.created_at.toISOString(),
        updated_at: user.updated_at.toISOString(),
      };
      const hasPermission = await rbacService.hasPermission(
        userWithRole,
        "hotel_management:delete" as any
      );

      if (!hasPermission) {
        return res.status(403).json({
          error: "Insufficient permissions",
          required_permission: "hotel_management:delete",
        });
      }
    } else {
      return res.status(401).json({ error: "Authentication required" });
    }

    const { result } = await DeleteHotelWorkflow(req.scope).run({
      //@ts-ignore
      input: req.body,
    });

    res.json({ hotel: result });
  } catch (error) {
    console.error("Error deleting hotel:", error);
    res.status(500).json({ error: "Failed to delete hotel" });
  }
};

export const GET = async (
  req: AuthenticatedMedusaRequest,
  res: MedusaResponse
) => {
  try {
    const query = req.scope.resolve(ContainerRegistrationKeys.QUERY);
    const rbacService: RbacModuleService = req.scope.resolve(RBAC_MODULE);
    const userService = req.scope.resolve(Modules.USER);

    // Check permission to view hotels
    let userWithRole = null;
    if (req.auth_context?.actor_id) {
      try {
        userWithRole = await userService.retrieveUser(
          req.auth_context.actor_id
        );

        // Check if user has permission to view hotels
        const hasPermission = await rbacService.hasPermission(
          userWithRole,
          "hotel_management:view" as any
        );
        if (!hasPermission) {
          return res.status(403).json({
            error: "Insufficient permissions",
            required_permission: "hotel_management:view",
          });
        }
      } catch (error) {
        console.warn("Could not load user RBAC data:", error);
        return res.status(403).json({ error: "Authentication required" });
      }
    }

    const { limit = 20, offset = 0, is_featured } = req.query || {};
    const filters: Record<string, any> = {};

    if (is_featured !== undefined) {
      filters.is_featured = is_featured;
    }

    // Get all hotels with destination names via JOIN
    const {
      data: allHotels,
      metadata: { count: totalCount, take, skip },
    } = await query.graph({
      entity: "hotel",
      fields: ["*", "roomConfigs.id", "roomConfigs.name", "images.*"],
      filters,
      pagination: {
        skip: Number(offset),
        take: Number(limit),
      },
    });

    // Filter hotels based on user permissions
    let filteredHotels = allHotels;
    let filteredCount = totalCount;

    if (userWithRole) {
      const rbacData = userWithRole.metadata?.rbac as any;

      // All users with valid roles can see all hotels
      // Access control is now permission-based, not hotel-based
      if (rbacData?.role !== UserRole.ADMIN) {
        // Non-admin users see all hotels but with permission-based operations
        filteredHotels = allHotels;
        filteredCount = allHotels.length;
      }
    }

    // Fetch destination names for the filtered hotels
    const destinationIds = [...new Set(filteredHotels.map((hotel: any) => hotel.destination_id).filter(Boolean))];
    let destinationMap = new Map<string, string>();

    if (destinationIds.length > 0) {
      try {
        const { data: destinations } = await query.graph({
          entity: "destination",
          filters: { id: { $in: destinationIds } },
          fields: ["id", "name"],
        });

        if (destinations && destinations.length > 0) {
          destinations.forEach((destination: { id: string; name: string }) => {
            destinationMap.set(destination.id, destination.name);
          });
        }
      } catch (error) {
        console.error("Error fetching destination names:", error);
      }
    }

     // Add destination names to hotels
    const hotelsWithDestinations = filteredHotels.map((hotel: any) => ({
      ...hotel,
      destination_name: hotel.destination_id ? destinationMap.get(hotel.destination_id) : null,
    }));

    const response = {
      hotels: hotelsWithDestinations || [],
      count: filteredCount || 0,
      limit: take || Number(limit),
      offset: skip || Number(offset),
    };

    res.json(response);
  } catch (error) {
    console.error("Error in hotel listing:", error);
    res.status(500).json({
      type: "server_error",
      message: "Failed to list hotels",
    });
  }
};
